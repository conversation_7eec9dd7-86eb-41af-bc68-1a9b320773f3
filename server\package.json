{"name": "deepchat-server", "version": "1.0.0", "description": "DeepChat Backend Server", "main": "dist/index.js", "type": "module", "scripts": {"build": "node scripts/build-server.js", "start": "node scripts/start-server.js", "dev": "nodemon --exec \"tsx\" index.ts", "dev:admin": "nodemon --exec \"tsx\" index.ts", "init:admin": "node scripts/init-admin.cjs", "test": "npx tsx tests/test-server.ts", "test:docs": "npx tsx tests/test-api-docs.ts", "test:file": "npx tsx tests/test-file-upload.ts", "test:admin": "npx tsx tests/test-admin-api.ts"}, "dependencies": {"@types/form-data": "^2.5.2", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "form-data": "^4.0.4", "jsonwebtoken": "^9.0.1", "mongoose": "^7.5.0", "multer": "^1.4.5-lts.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1"}, "devDependencies": {"@types/bcrypt": "^5.0.0", "@types/cors": "^2.8.13", "@types/express": "^4.17.17", "@types/jsonwebtoken": "^9.0.2", "@types/node": "^20.4.5", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "cross-env": "^10.0.0", "nodemon": "^3.0.1", "ts-node": "^10.9.1", "tsx": "^4.20.5", "typescript": "^5.1.6"}}