const mongoose = require('mongoose');
const dotenv = require('dotenv');
const bcrypt = require('bcryptjs');

// 加载环境变量
dotenv.config();

// 用户模型
const userSchema = new mongoose.Schema({
  username: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    minlength: 3,
    maxlength: 30
  },
  email: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, '请输入有效的邮箱地址']
  },
  password: {
    type: String,
    required: true,
    minlength: 6
  },
  avatar: {
    type: String,
    default: ''
  }
}, {
  timestamps: true
});

const User = mongoose.model('User', userSchema);

// 连接数据库
const connectDB = async () => {
  try {
    const conn = await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/deepchat');
    console.log(`MongoDB Connected: ${conn.connection.host}`);
  } catch (error) {
    console.error('Error connecting to MongoDB:', error);
    process.exit(1);
  }
};

// 创建默认管理员用户
const createDefaultAdmin = async () => {
  try {
    // 检查是否已经存在用户
    const userCount = await User.countDocuments();
    
    if (userCount === 0) {
      // 密码加密
      const saltRounds = 10;
      const hashedPassword = await bcrypt.hash('admin', saltRounds);
      
      // 创建默认管理员用户
      const adminUser = new User({
        username: 'admin',
        email: '<EMAIL>',
        password: hashedPassword
      });
      
      await adminUser.save();
      console.log('默认管理员用户创建成功:');
      console.log('用户名: admin');
      console.log('密码: admin');
      console.log('请登录后立即修改密码!');
    } else {
      console.log('数据库中已存在用户，跳过默认管理员用户创建');
    }
  } catch (error) {
    console.error('创建默认管理员用户时出错:', error);
  }
};

// 初始化数据库
const initDatabase = async () => {
  await connectDB();
  await createDefaultAdmin();
  await mongoose.connection.close();
  console.log('数据库初始化完成');
};

// 运行初始化
initDatabase().catch(error => {
  console.error('初始化过程中出错:', error);
  process.exit(1);
});