#!/usr/bin/env node

// 启动脚本 - 启动编译后的后端服务器
import { spawn } from 'child_process';
import { join } from 'path';
import { cwd } from 'process';

console.log('开始启动后端服务器...');

try {
  // 启动服务器进程
  const serverProcess = spawn('node', [join('..', 'dist', 'server', 'index.js')], {
    stdio: 'inherit',
    env: { ...process.env, NODE_ENV: 'production' }
  });

  serverProcess.on('error', (error) => {
    console.error('❌ 启动服务器时发生错误:', error);
    process.exit(1);
  });

  serverProcess.on('exit', (code) => {
    if (code === 0) {
      console.log('✅ 服务器正常退出');
    } else {
      console.log(`❌ 服务器异常退出，退出码: ${code}`);
    }
    process.exit(code || 0);
  });

  // 监听SIGINT信号（Ctrl+C）
  process.on('SIGINT', () => {
    console.log('收到SIGINT信号，正在关闭服务器...');
    serverProcess.kill('SIGTERM');
  });

  // 监听SIGTERM信号
  process.on('SIGTERM', () => {
    console.log('收到SIGTERM信号，正在关闭服务器...');
    serverProcess.kill('SIGTERM');
  });

} catch (error) {
  console.error('❌ 启动过程中发生错误:', error);
  process.exit(1);
}