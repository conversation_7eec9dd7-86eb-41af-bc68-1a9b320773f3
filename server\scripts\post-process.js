#!/usr/bin/env node

// 后处理脚本 - 修复编译后的JavaScript文件中的导入路径
import { readdirSync, readFileSync, writeFileSync } from 'fs';
import { join } from 'path';

console.log('开始后处理编译后的文件...');

const distDir = join('..', 'dist', 'server');

// 递归处理目录中的所有.js文件
function processDirectory(dir) {
  const files = readdirSync(dir, { withFileTypes: true });
  
  for (const file of files) {
    const fullPath = join(dir, file.name);
    
    if (file.isDirectory()) {
      processDirectory(fullPath);
    } else if (file.isFile() && file.name.endsWith('.js')) {
      processFile(fullPath);
    }
  }
}

// 处理单个.js文件
function processFile(filePath) {
  try {
    const content = readFileSync(filePath, 'utf8');
    
    // 将导入语句中的.ts扩展名替换为.js扩展名
    const updatedContent = content
      .replace(/from\s+['"](\..*?)\.ts['"]/g, 'from "$1.js"')
      .replace(/import\s+(\w+)\s+from\s+['"](\..*?)\.ts['"]/g, 'import $1 from "$2.js"')
      .replace(/import\s+\*\s+as\s+(\w+)\s+from\s+['"](\..*?)\.ts['"]/g, 'import * as $1 from "$2.js"')
      .replace(/import\s+['"](\..*?)\.ts['"]/g, 'import "$1.js"');
    
    // 如果内容有变化，则写入文件
    if (updatedContent !== content) {
      writeFileSync(filePath, updatedContent, 'utf8');
      console.log(`✅ 已更新文件: ${filePath}`);
    }
  } catch (error) {
    console.error(`❌ 处理文件时出错 ${filePath}:`, error.message);
  }
}

// 处理整个dist目录
processDirectory(distDir);

console.log('🎉 后处理完成!');