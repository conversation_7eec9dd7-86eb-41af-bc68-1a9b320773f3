import express, { Router } from 'express';
import { uploadFile, getFile } from '../controllers/file.controller';
import { authenticateToken } from '../middleware/auth.middleware';
import multer from 'multer';
import fs from 'fs';
import path from 'path';

// 配置 multer 存储
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    // 临时存储目录
    const tempDir = path.join(__dirname, '..', '..', 'temp');
    
    // 确保临时目录存在
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }
    
    cb(null, tempDir);
  },
  filename: (req, file, cb) => {
    // 生成临时文件名
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix);
  }
});

const upload = multer({ storage: storage });

const router: Router = express.Router();

// 文件上传路由（需要认证）
router.post('/upload', authenticateToken, upload.single('file'), uploadFile);

// 获取文件信息路由（需要认证）
router.get('/:id', authenticateToken, getFile);

export default router;