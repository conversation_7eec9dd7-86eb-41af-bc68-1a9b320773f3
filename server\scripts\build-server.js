#!/usr/bin/env node

// 构建脚本 - 编译TypeScript代码到JavaScript
import { execSync } from 'child_process';
import { existsSync, copyFileSync, writeFileSync, mkdirSync } from 'fs';
import { join } from 'path';

console.log('开始构建后端服务器...');

try {
  // 编译TypeScript代码
  console.log('正在编译TypeScript代码...');
  execSync('tsc -p tsconfig.server.json', { 
    stdio: 'inherit' 
  });

  console.log('✅ TypeScript编译完成');

  // 复制.env文件到dist目录
  const envSource = join('.env');
  const envDest = join('..', 'dist', 'server', '.env');
  
  if (existsSync(envSource)) {
    copyFileSync(envSource, envDest);
    console.log('✅ .env文件复制完成');
  }

  // 创建dist/server/package.json文件
  const packageJson = {
    "type": "module",
    "dependencies": {
      "bcrypt": "^5.1.0",
      "cors": "^2.8.5",
      "dotenv": "^16.3.1",
      "express": "^4.18.2",
      "jsonwebtoken": "^9.0.1",
      "mongoose": "^7.5.0",
      "swagger-jsdoc": "^6.2.8",
      "swagger-ui-express": "^5.0.1"
    }
  };
  const packageJsonPath = join('..', 'dist', 'server', 'package.json');
  // 确保目录存在
  try {
    mkdirSync(join('..', 'dist', 'server'), { recursive: true });
  } catch (err) {
    // 目录可能已存在
  }
  writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
  console.log('✅ package.json文件创建完成');

  // 后处理编译后的文件
  console.log('正在后处理编译后的文件...');
  execSync('node scripts/post-process.js', { 
    stdio: 'inherit' 
  });
  console.log('✅ 后处理完成');

  console.log('🎉 后端服务器构建完成!');
  console.log('_DIST目录结构_:');
  
  // 显示dist目录结构
  try {
    execSync('ls -la ../dist/server/', { 
      stdio: 'inherit' 
    });
  } catch (error) {
    console.log('⚠️  无法列出dist目录内容');
  }

} catch (error) {
  console.error('❌ 构建过程中发生错误:', error.message);
  process.exit(1);
}