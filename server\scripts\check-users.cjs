const mongoose = require('mongoose');
const dotenv = require('dotenv');

// 加载环境变量
dotenv.config();

// 用户模型
const userSchema = new mongoose.Schema({
  username: String,
  email: String,
  password: String,
  avatar: String
}, {
  timestamps: true
});

const User = mongoose.model('User', userSchema);

// 连接数据库
const connectDB = async () => {
  try {
    const conn = await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/deepchat');
    console.log(`MongoDB Connected: ${conn.connection.host}`);
    return conn;
  } catch (error) {
    console.error('Error connecting to MongoDB:', error);
    process.exit(1);
  }
};

// 检查现有用户
const checkUsers = async () => {
  const conn = await connectDB();
  
  try {
    const users = await User.find({});
    console.log('现有用户:');
    users.forEach(user => {
      console.log(`- 用户名: ${user.username}, 邮箱: ${user.email}, ID: ${user._id}`);
    });
    
    await conn.connection.close();
  } catch (error) {
    console.error('查询用户时出错:', error);
    await conn.connection.close();
  }
};

checkUsers();