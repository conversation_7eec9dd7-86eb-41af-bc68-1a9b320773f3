const mongoose = require('mongoose');
const dotenv = require('dotenv');
const bcrypt = require('bcryptjs');

// 加载环境变量
dotenv.config();

// 用户模型
const userSchema = new mongoose.Schema({
  username: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    minlength: 3,
    maxlength: 30
  },
  email: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, '请输入有效的邮箱地址']
  },
  password: {
    type: String,
    required: true,
    minlength: 6
  },
  avatar: {
    type: String,
    default: ''
  }
}, {
  timestamps: true
});

const User = mongoose.model('User', userSchema);

// 连接数据库
const connectDB = async () => {
  try {
    const conn = await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/deepchat');
    console.log(`MongoDB Connected: ${conn.connection.host}`);
    return conn;
  } catch (error) {
    console.error('Error connecting to MongoDB:', error);
    process.exit(1);
  }
};

// 创建管理员用户
const createAdminUser = async () => {
  const conn = await connectDB();
  
  try {
    // 检查是否已存在管理员用户
    const existingAdmin = await User.findOne({ username: 'admin' });
    if (existingAdmin) {
      console.log('管理员用户已存在');
      await conn.connection.close();
      return;
    }
    
    // 密码加密
    const saltRounds = 10;
    const hashedPassword = await bcrypt.hash('admin', saltRounds);
    
    // 创建管理员用户
    const adminUser = new User({
      username: 'admin',
      email: '<EMAIL>',
      password: hashedPassword
    });
    
    await adminUser.save();
    console.log('管理员用户创建成功:');
    console.log('用户名: admin');
    console.log('密码: admin');
    console.log('请登录后立即修改密码!');
    
    await conn.connection.close();
  } catch (error) {
    console.error('创建管理员用户时出错:', error);
    await conn.connection.close();
  }
};

createAdminUser();